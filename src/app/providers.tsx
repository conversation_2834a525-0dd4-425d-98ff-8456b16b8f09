"use client";

import { Hero<PERSON><PERSON>rovider } from "@heroui/react";
import { ToastProvider } from "@heroui/toast";
import { ThemeProvider } from "next-themes";
import React, { useEffect, useState, useRef } from "react";
import { initAuthClient } from "@quote/auth-client/react";
import { showErrorToast, showSuccessToast } from "@/utils/toast";
import FullScreenLoading from "@/components/ui/loading/FullScreenLoading";
import {
  getSensitiveHostnames,
  isCurrentHostnameSensitive,
} from "@/utils/auth";
import { useAuthActions } from "@/hooks/useAuthActions";
import { usePathname } from "next/navigation";
import logger from "@/utils/logger";
import { systemService } from "@/lib/api/services/systemService";

// 全局变量跟踪初始化状态
let isAuthInitialized = false;

const authLogger = logger.createPrefixed("Auth");
const systemLogger = logger.createPrefixed("System");
// 初始化认证客户端的函数
const initializeAuth = () => {
  if (isAuthInitialized || typeof window === "undefined") return;

  try {
    // 使用环境变量
    // 根据新的SDK命名规范更新变量名
    const authServiceApiUrl = process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL;
    const authServiceRedirectUrl =
      process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL;

    if (!authServiceApiUrl || !authServiceRedirectUrl) {
      throw new Error(
        "Auth Service API URL 或 Auth Service Redirect URL 未设置"
      );
    }

    // 使用公共函数获取敏感域名列表
    const sensitiveHostnames = getSensitiveHostnames();

    // 添加敏感域名列表配置
    initAuthClient(authServiceApiUrl, authServiceRedirectUrl, {
      sensitiveHostnames,
    });
    isAuthInitialized = true;
    authLogger.log("认证客户端初始化成功，已配置敏感域名");
    return null;
  } catch (error) {
    authLogger.error("认证客户端初始化失败:", error);
    return error instanceof Error ? error.message : "认证服务初始化失败";
  }
};

// 添加一个认证状态包装组件
function AuthStateProvider({ children }: { children: React.ReactNode }) {
  // 使用统一的认证hook，现在包含 SSO 重定向检查功能
  const { isAuthenticated, loading, statusCode, checkSSORedirect } =
    useAuthActions();
  // 检查当前是否为敏感域名
  const [isSensitiveHost, setIsSensitiveHost] = useState(false);
  // 是否已完成敏感域名检查
  const [hostCheckComplete, setHostCheckComplete] = useState(false);
  // 获取当前路径，用于监听路由变化
  const pathname = usePathname();
  // 跟踪是否已经在开发环境下设置过用户ID
  const [hasSetUserId, setHasSetUserId] = useState(false);
  // 使用 useRef 跟踪是否已经处理过 SSO 重定向，避免重复执行
  const ssoRedirectHandled = useRef(false);
  // 使用 useRef 跟踪之前的认证状态，避免重复日志
  const prevAuthState = useRef<{
    isAuthenticated: boolean;
    statusCode: number | undefined;
    loading: boolean;
  }>({
    isAuthenticated: false,
    statusCode: undefined,
    loading: true,
  });

  // 检查当前是否为敏感域名的函数
  const checkSensitiveHost = () => {
    if (typeof window !== "undefined") {
      try {
        const sensitive = isCurrentHostnameSensitive();
        setIsSensitiveHost(sensitive);
        authLogger.log("当前域名是否为敏感域名:", sensitive, "路径:", pathname);
      } catch (error) {
        authLogger.error("检查敏感域名时出错:", error);
      } finally {
        setHostCheckComplete(true);
      }
    }
  };

  // 初始检查
  useEffect(() => {
    checkSensitiveHost();
  }, []);

  // 当路径变化时重新检查
  useEffect(() => {
    checkSensitiveHost();
  }, [pathname]);

  // SSO 重定向检查和处理
  useEffect(() => {
    const handleSSORedirect = async () => {
      // 只在客户端执行，且尚未处理过 SSO 重定向
      if (typeof window === "undefined" || ssoRedirectHandled.current) return;

      try {
        // 检查是否是 SSO 重定向
        const ssoState = checkSSORedirect();

        // 添加详细日志以调试
        authLogger.log("SSO重定向检查结果:", {
          isSSORedirect: ssoState.isSSORedirect,
          currentURL: window.location.href,
          ssoRedirectHandled: ssoRedirectHandled.current,
        });

        if (ssoState.isSSORedirect) {
          authLogger.log("检测到 SSO 重定向，清除SSO标记");
          ssoRedirectHandled.current = true;

          // 清除 SSO 标记
          ssoState.clear();

          // 注意：不再手动调用 verifyStatus()，因为 useAuth() hook 会自动处理认证状态
          // 这样可以避免重复的 verify_status 请求
          authLogger.log("SSO 重定向处理完成，等待 useAuth 自动验证");

          // 延迟显示成功消息，等待认证状态更新
          setTimeout(() => {
            if (isAuthenticated) {
              showSuccessToast("Welcome back! Login successful.");
            }
          }, 1000);
        } else {
          authLogger.log("非 SSO 重定向，跳过额外的认证验证");
        }
      } catch (error) {
        authLogger.error("处理 SSO 重定向时出错:", error);
      }
    };

    handleSSORedirect();
  }, []); // 移除依赖数组中的函数引用，只在组件挂载时执行一次

  // 检测认证状态变化
  useEffect(() => {
    const currentState = {
      isAuthenticated,
      statusCode,
      loading,
    };

    // 只在认证完成且状态真正发生变化时记录日志
    if (!loading && !prevAuthState.current.loading) {
      // 检查认证状态是否发生变化
      const authChanged =
        prevAuthState.current.isAuthenticated !== isAuthenticated;
      const statusChanged = prevAuthState.current.statusCode !== statusCode;

      if (authChanged || statusChanged) {
        if (isAuthenticated) {
          authLogger.log("认证状态变化 -> 认证通过，状态码:", statusCode);
        } else {
          authLogger.log("认证状态变化 -> 认证未通过，状态码:", statusCode);
        }
      }
    } else if (!loading && prevAuthState.current.loading) {
      // 首次认证完成
      if (isAuthenticated) {
        authLogger.log("首次认证完成 -> 认证通过，状态码:", statusCode);

        // 开发环境下，如果用户已登录且尚未设置过用户ID，则调用setUserId
        // if (process.env.NODE_ENV === "development" && !hasSetUserId) {
        //   systemService
        //     .setUserId({ user_id: "e4eb5524-6e44-4643-b9f7-c78bbc7f3b8b" })
        //     .then(() => {
        //       systemLogger.log("开发环境：用户ID已设置到Cookie");
        //       setHasSetUserId(true);
        //     })
        //     .catch((error) => {
        //       systemLogger.error("开发环境：设置用户ID失败:", error);
        //     });
        // }
      } else {
        authLogger.log("首次认证完成 -> 认证未通过，状态码:", statusCode);
      }
    }

    // 处理登出时的状态重置
    if (!loading && !isAuthenticated && prevAuthState.current.isAuthenticated) {
      // 用户退出登录时，重置hasSetUserId状态和SSO处理状态
      // if (hasSetUserId) {
      //   setHasSetUserId(false);
      //   systemLogger.log("开发环境：用户退出登录，重置用户ID设置状态");
      // }
      // 重置 SSO 重定向处理状态，以便下次登录时能够正常处理
      if (ssoRedirectHandled.current) {
        ssoRedirectHandled.current = false;
        authLogger.log("重置 SSO 重定向处理状态");
      }
    }

    // 更新之前的状态
    prevAuthState.current = currentState;
  }, [loading, isAuthenticated, statusCode, hasSetUserId]);

  // 如果敏感域名检查尚未完成，显示加载状态
  if (!hostCheckComplete) {
    return <FullScreenLoading />;
  }

  // 敏感域名检查已完成
  if (isSensitiveHost) {
    // 如果是敏感域名
    if (loading) {
      // 认证状态正在加载中，显示加载状态
      return <FullScreenLoading />;
    }

    if (!isAuthenticated) {
      // 认证未通过，显示加载状态（此时认证系统应该会自动跳转到登录页）
      return <FullScreenLoading />;
    }

    // 认证通过，显示子组件
    return <>{children}</>;
  } else {
    // 非敏感域名
    if (loading) {
      // 认证状态正在加载中，显示加载状态
      return <FullScreenLoading />;
    }

    // 非敏感域名，无论认证状态如何都显示子组件
    return <>{children}</>;
  }
}

export default function Providers({ children }: { children: React.ReactNode }) {
  const [initError, setInitError] = useState<string | null>(null);

  // 初始化认证客户端
  useEffect(() => {
    const error = initializeAuth();
    if (error) {
      setInitError(error);
    }
  }, []);

  // 显示初始化错误
  useEffect(() => {
    if (initError) {
      showErrorToast(`认证服务初始化失败: ${initError}`);
    }
  }, [initError]);

  return (
    <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
      <HeroUIProvider>
        <ToastProvider
          placement="top-center"
          maxVisibleToasts={3}
          toastProps={{
            color: "primary",
            variant: "flat",
            radius: "md",
            timeout: 4000,
          }}
        />
        <AuthStateProvider>{children}</AuthStateProvider>
      </HeroUIProvider>
    </ThemeProvider>
  );
}
